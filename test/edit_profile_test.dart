import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:soho_souk/pages/edit_profile_page/edit_profile_page_widget.dart';
import 'package:soho_souk/store/AppStore.dart';

void main() {
  group('Edit Profile Page Tests', () {
    late AppStore appStore;

    setUp(() {
      appStore = AppStore();
      // Mock user data
      appStore.user_id = 1;
      appStore.user_name = '<PERSON>';
      appStore.user_email = '<EMAIL>';
      appStore.user_mobile = '1234567890';
      appStore.user_instagram = '@johndoe';
      appStore.user_x_com = '@johndoe_x';
      appStore.user_facebook = 'john.doe.facebook';
      appStore.profile_image = 'profile.jpg';
      appStore.logined = true;
    });

    testWidgets('Edit Profile Page should populate all fields correctly', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: EditProfilePageWidget(),
        ),
      );

      // Wait for the widget to build and any async operations to complete
      await tester.pumpAndSettle();

      // Verify that text fields are present
      expect(find.byType(TextFormField), findsWidgets);

      // Check if the page has the expected structure
      expect(find.text('Edit Profile'), findsOneWidget);
      expect(find.text('Personal Information'), findsOneWidget);
      expect(find.text('Social Media Links'), findsOneWidget);
      expect(find.text('Save Changes'), findsOneWidget);

      // Verify field labels are present
      expect(find.text('First Name'), findsOneWidget);
      expect(find.text('Last Name'), findsOneWidget);
      expect(find.text('Email Address'), findsOneWidget);
      expect(find.text('Mobile Number'), findsOneWidget);
      expect(find.text('Instagram'), findsOneWidget);
      expect(find.text('X.com (Twitter)'), findsOneWidget);
      expect(find.text('Facebook'), findsOneWidget);
    });

    testWidgets('Text controllers should be properly initialized', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: EditProfilePageWidget(),
        ),
      );

      await tester.pumpAndSettle();

      // Find all TextFormField widgets
      final textFields = find.byType(TextFormField);
      expect(textFields, findsWidgets);

      // Verify that we have the expected number of text fields (7 fields)
      expect(tester.widgetList(textFields).length, equals(7));
    });

    testWidgets('Save button should be functional', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: EditProfilePageWidget(),
        ),
      );

      await tester.pumpAndSettle();

      // Find the save button
      final saveButton = find.text('Save Changes');
      expect(saveButton, findsOneWidget);

      // Verify the button is tappable
      await tester.tap(saveButton);
      await tester.pump();
    });

    testWidgets('Profile image section should be present', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: EditProfilePageWidget(),
        ),
      );

      await tester.pumpAndSettle();

      // Check for profile image related elements
      expect(find.text('Update Profile Picture'), findsOneWidget);
      expect(find.byIcon(Icons.camera_alt), findsOneWidget);
    });

    testWidgets('Form validation should work for required fields', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: EditProfilePageWidget(),
        ),
      );

      await tester.pumpAndSettle();

      // Clear all text fields to test validation
      final textFields = find.byType(TextFormField);
      for (int i = 0; i < tester.widgetList(textFields).length; i++) {
        await tester.enterText(textFields.at(i), '');
      }

      // Try to save with empty fields
      final saveButton = find.text('Save Changes');
      await tester.tap(saveButton);
      await tester.pump();

      // The validation should prevent saving (though we can't easily test the exact error messages in this setup)
    });
  });
}
