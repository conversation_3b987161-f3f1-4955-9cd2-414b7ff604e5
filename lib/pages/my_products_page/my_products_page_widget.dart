import 'package:nb_utils/nb_utils.dart';
import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/main.dart';
import 'package:soho_souk/models/PostModel.dart';
import 'package:http/http.dart' as http;
import 'package:soho_souk/pages/app_button/app_button_widget.dart';
import 'package:soho_souk/widget/loader.dart';
import '../../Classified_App/classified_app_theme.dart';
import '../../Classified_App/classified_app_util.dart';
import '/pages/app_bar/app_bar_widget.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'my_products_page_model.dart';
export 'my_products_page_model.dart';

class MyProductsPageWidget extends StatefulWidget {
  const MyProductsPageWidget({super.key});

  @override
  State<MyProductsPageWidget> createState() => _MyProductsPageWidgetState();
}

class _MyProductsPageWidgetState extends State<MyProductsPageWidget> {
  late MyProductsPageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  // Infinite scroll variables
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;
  int _currentPage = 1;
  final int _postsPerPage = 10;
  List<PostModel> _displayedPosts = [];
  bool _hasMorePosts = true;

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => MyProductsPageModel());
    _initializePosts();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _model.dispose();
    super.dispose();
  }

  bool loader = true;

  getLatestPost() async {
    // Implementation for getting latest posts
  }

  void deletePost(post_id) async {
    showLoadingDialog(context);
    try {
      final response = await http.get(
          Uri.parse("${ApiUtils.BASE_URL}delete-postad/${post_id}"),
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          });
      Navigator.of(context, rootNavigator: true).pop(false);
      if (response.statusCode == 200) {
        appStore.deletePost(post_id);
        toasty(context, "Post Deleted",
            bgColor: Colors.green, textColor: Colors.black);
        setState(() {});
      } else {
        print('Error Occurred');
      }
    } catch (e) {
      setState(() {
        loader = false;
      });
      print('Error Occurred product' + e.toString());
    }
  }

  // Initialize posts for infinite scroll
  void _initializePosts() {
    final allUserPosts = appStore.userPosts;
    _displayedPosts.clear();

    if (allUserPosts.isNotEmpty) {
      final endIndex = (_postsPerPage < allUserPosts.length)
          ? _postsPerPage
          : allUserPosts.length;
      _displayedPosts.addAll(allUserPosts.take(endIndex));
      _hasMorePosts = allUserPosts.length > _postsPerPage;
    } else {
      _hasMorePosts = false;
    }

    if (mounted) {
      setState(() {});
    }
  }

  // Handle scroll events for infinite scroll
  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMorePosts();
    }
  }

  // Load more posts
  void _loadMorePosts() {
    if (_isLoadingMore || !_hasMorePosts) return;

    setState(() {
      _isLoadingMore = true;
    });

    // Simulate loading delay (you can remove this in production)
    Future.delayed(Duration(milliseconds: 500), () {
      final allUserPosts = appStore.userPosts;
      final currentLength = _displayedPosts.length;
      final remainingPosts = allUserPosts.length - currentLength;

      if (remainingPosts > 0) {
        final nextBatch = allUserPosts.skip(currentLength).take(_postsPerPage);
        _displayedPosts.addAll(nextBatch);
        _currentPage++;

        // Check if there are more posts to load
        _hasMorePosts = _displayedPosts.length < allUserPosts.length;
      } else {
        _hasMorePosts = false;
      }

      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    // Reinitialize posts if the user posts have changed
    if (_displayedPosts.isEmpty && appStore.userPosts.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _initializePosts();
      });
    }

    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: ClassifiedAppTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              wrapWithModel(
                model: _model.appBarModel,
                updateCallback: () => setState(() {}),
                child: AppBarWidget(
                  title: 'My Post ads',
                ),
              ),
              _displayedPosts.isNotEmpty
                  ? Expanded(
                      child: Stack(
                        alignment: AlignmentDirectional(0.0, 1.0),
                        children: [
                          Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                16.0, 0.0, 16.0, 0.0),
                            child: CustomScrollView(
                              controller: _scrollController,
                              slivers: [
                                SliverPadding(
                                  padding:
                                      EdgeInsets.fromLTRB(0, 20.0, 0, 20.0),
                                  sliver: SliverGrid(
                                    gridDelegate:
                                        SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: () {
                                        if (MediaQuery.sizeOf(context).width <
                                            kBreakpointSmall) {
                                          return 2;
                                        } else if (MediaQuery.sizeOf(context)
                                                .width <
                                            kBreakpointMedium) {
                                          return 3;
                                        } else if (MediaQuery.sizeOf(context)
                                                .width <
                                            kBreakpointLarge) {
                                          return 4;
                                        } else {
                                          return 5;
                                        }
                                      }(),
                                      crossAxisSpacing: 12.0,
                                      mainAxisSpacing: 16.0,
                                      childAspectRatio: 0.75,
                                    ),
                                    delegate: SliverChildBuilderDelegate(
                                      (context, index) {
                                        final recentPost =
                                            _displayedPosts[index];
                                        return GestureDetector(
                                          onTap: () {
                                            context.pushNamed(
                                              'ProductDetailPage',
                                              queryParameters: {
                                                'post': jsonEncode(
                                                    recentPost.toJson()),
                                              }.withoutNulls,
                                              extra: <String, dynamic>{
                                                kTransitionInfoKey:
                                                    TransitionInfo(
                                                  hasTransition: true,
                                                  transitionType:
                                                      PageTransitionType
                                                          .rightToLeft,
                                                  duration: Duration(
                                                      milliseconds: 300),
                                                ),
                                              },
                                            );
                                          },
                                          child: Container(
                                            width: double.infinity,
                                            decoration: BoxDecoration(
                                              color:
                                                  ClassifiedAppTheme.of(context)
                                                      .secondaryBackground,
                                              boxShadow: [
                                                BoxShadow(
                                                  blurRadius: 8.0,
                                                  color: Color(0x15000000),
                                                  offset: Offset(0.0, 2.0),
                                                  spreadRadius: 0.0,
                                                )
                                              ],
                                              borderRadius:
                                                  BorderRadius.circular(16.0),
                                              border: Border.all(
                                                color: Color(0x08000000),
                                                width: 1.0,
                                              ),
                                            ),
                                            child: Column(
                                              mainAxisSize: MainAxisSize.max,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Expanded(
                                                  flex: 3,
                                                  child: Stack(
                                                    children: [
                                                      ClipRRect(
                                                        borderRadius:
                                                            BorderRadius.only(
                                                          topLeft:
                                                              Radius.circular(
                                                                  16.0),
                                                          topRight:
                                                              Radius.circular(
                                                                  16.0),
                                                        ),
                                                        child: Container(
                                                          width:
                                                              double.infinity,
                                                          height:
                                                              double.infinity,
                                                          child: recentPost
                                                                          .image !=
                                                                      null &&
                                                                  recentPost
                                                                      .image!
                                                                      .isNotEmpty
                                                              ? CachedNetworkImage(
                                                                  width: double
                                                                      .infinity,
                                                                  height: double
                                                                      .infinity,
                                                                  fit: BoxFit
                                                                      .cover,
                                                                  imageUrl:
                                                                      "${ApiUtils.post_image}${recentPost.image}",
                                                                  placeholder: (context,
                                                                          url) =>
                                                                      Container(
                                                                    color: Color(
                                                                        0xFFF5F5F5),
                                                                    child: Icon(
                                                                      Icons
                                                                          .image,
                                                                      color: Color(
                                                                          0xFF9E9E9E),
                                                                      size:
                                                                          32.0,
                                                                    ),
                                                                  ),
                                                                  errorWidget: (context,
                                                                          url,
                                                                          error) =>
                                                                      Container(
                                                                    color: Color(
                                                                        0xFFF5F5F5),
                                                                    child: Icon(
                                                                      Icons
                                                                          .image_not_supported,
                                                                      color: Color(
                                                                          0xFF9E9E9E),
                                                                      size:
                                                                          32.0,
                                                                    ),
                                                                  ),
                                                                )
                                                              : Container(
                                                                  color: Color(
                                                                      0xFFF5F5F5),
                                                                  child: Icon(
                                                                    Icons.image,
                                                                    color: Color(
                                                                        0xFF9E9E9E),
                                                                    size: 32.0,
                                                                  ),
                                                                ),
                                                        ),
                                                      ),
                                                      // Edit button - Left corner
                                                      Positioned(
                                                        top: 6.0,
                                                        left: 6.0,
                                                        child: GestureDetector(
                                                          onTap: () {
                                                            context.pushNamed(
                                                              'EditPost',
                                                              queryParameters: {
                                                                'id':
                                                                    serializeParam(
                                                                  recentPost.id,
                                                                  ParamType.int,
                                                                ),
                                                              }.withoutNulls,
                                                              extra: <String,
                                                                  dynamic>{
                                                                kTransitionInfoKey:
                                                                    TransitionInfo(
                                                                  hasTransition:
                                                                      true,
                                                                  transitionType:
                                                                      PageTransitionType
                                                                          .rightToLeft,
                                                                  duration: Duration(
                                                                      milliseconds:
                                                                          300),
                                                                ),
                                                              },
                                                            );
                                                          },
                                                          child: Container(
                                                            width: 28.0,
                                                            height: 28.0,
                                                            decoration:
                                                                BoxDecoration(
                                                              color:
                                                                  Colors.white,
                                                              shape: BoxShape
                                                                  .circle,
                                                              boxShadow: [
                                                                BoxShadow(
                                                                  blurRadius:
                                                                      3.0,
                                                                  color: Color(
                                                                      0x15000000),
                                                                  offset:
                                                                      Offset(
                                                                          0.0,
                                                                          1.0),
                                                                )
                                                              ],
                                                            ),
                                                            child: Icon(
                                                              Icons.edit,
                                                              size: 14.0,
                                                              color: ClassifiedAppTheme
                                                                      .of(context)
                                                                  .primary,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      // Delete button - Right corner
                                                      Positioned(
                                                        top: 6.0,
                                                        right: 6.0,
                                                        child: GestureDetector(
                                                          onTap: () =>
                                                              showDialog(
                                                            context: context,
                                                            builder:
                                                                (dialogContext) {
                                                              return Dialog(
                                                                elevation: 0,
                                                                insetPadding:
                                                                    EdgeInsets
                                                                        .zero,
                                                                backgroundColor:
                                                                    Colors
                                                                        .transparent,
                                                                alignment: AlignmentDirectional(
                                                                        0.0,
                                                                        0.0)
                                                                    .resolve(
                                                                        Directionality.of(
                                                                            context)),
                                                                child:
                                                                    GestureDetector(
                                                                  onTap: () => _model
                                                                          .unfocusNode
                                                                          .canRequestFocus
                                                                      ? FocusScope.of(
                                                                              context)
                                                                          .requestFocus(_model
                                                                              .unfocusNode)
                                                                      : FocusScope.of(
                                                                              context)
                                                                          .unfocus(),
                                                                  child: deletePostWidget(
                                                                      _displayedPosts[index]
                                                                              .id ??
                                                                          0),
                                                                ),
                                                              );
                                                            },
                                                          ).then((value) =>
                                                                  setState(
                                                                      () {})),
                                                          child: Container(
                                                            width: 28.0,
                                                            height: 28.0,
                                                            decoration:
                                                                BoxDecoration(
                                                              color:
                                                                  Colors.white,
                                                              shape: BoxShape
                                                                  .circle,
                                                              boxShadow: [
                                                                BoxShadow(
                                                                  blurRadius:
                                                                      3.0,
                                                                  color: Color(
                                                                      0x15000000),
                                                                  offset:
                                                                      Offset(
                                                                          0.0,
                                                                          1.0),
                                                                )
                                                              ],
                                                            ),
                                                            child: Icon(
                                                              Icons.delete,
                                                              size: 14.0,
                                                              color: Colors.red,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                // Content section
                                                Expanded(
                                                  flex: 2,
                                                  child: Padding(
                                                    padding:
                                                        EdgeInsetsDirectional
                                                            .fromSTEB(12.0, 6.0,
                                                                12.0, 8.0),
                                                    child: Column(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        // Product name
                                                        AutoSizeText(
                                                          recentPost.post_name
                                                              .toString(),
                                                          maxLines: 1,
                                                          style:
                                                              ClassifiedAppTheme
                                                                      .of(context)
                                                                  .bodyMedium
                                                                  .override(
                                                                    fontFamily:
                                                                        'Satoshi',
                                                                    fontSize:
                                                                        12.0,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w600,
                                                                    useGoogleFonts:
                                                                        false,
                                                                  ),
                                                          minFontSize: 10.0,
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                        ),
                                                        SizedBox(height: 1.0),
                                                        // Price
                                                        Text(
                                                          "AED ${recentPost.price}",
                                                          style:
                                                              ClassifiedAppTheme
                                                                      .of(context)
                                                                  .bodyMedium
                                                                  .override(
                                                                    fontFamily:
                                                                        'Satoshi',
                                                                    fontSize:
                                                                        13.0,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                    color: ClassifiedAppTheme.of(
                                                                            context)
                                                                        .primary,
                                                                    useGoogleFonts:
                                                                        false,
                                                                  ),
                                                        ),
                                                        SizedBox(height: 2.0),
                                                        // Vendor info
                                                        Flexible(
                                                          child: Row(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .max,
                                                            children: [
                                                              ClipRRect(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            16.0),
                                                                child: recentPost
                                                                            .vendor_image ==
                                                                        ''
                                                                    ? Image
                                                                        .asset(
                                                                        'assets/images/soho_icon.jpg',
                                                                        width:
                                                                            20.0,
                                                                        height:
                                                                            20.0,
                                                                        fit: BoxFit
                                                                            .cover,
                                                                      )
                                                                    : CachedNetworkImage(
                                                                        width:
                                                                            20.0,
                                                                        height:
                                                                            20.0,
                                                                        fit: BoxFit
                                                                            .cover,
                                                                        imageUrl:
                                                                            "${ApiUtils.profile_files}${recentPost.vendor_image}",
                                                                        placeholder:
                                                                            (context, url) =>
                                                                                Container(
                                                                          color:
                                                                              Color(0xFFF5F5F5),
                                                                          child:
                                                                              Icon(
                                                                            Icons.person,
                                                                            color:
                                                                                Color(0xFF9E9E9E),
                                                                            size:
                                                                                16.0,
                                                                          ),
                                                                        ),
                                                                        errorWidget: (context,
                                                                                url,
                                                                                error) =>
                                                                            Container(
                                                                          color:
                                                                              Color(0xFFF5F5F5),
                                                                          child:
                                                                              Icon(
                                                                            Icons.person,
                                                                            color:
                                                                                Color(0xFF9E9E9E),
                                                                            size:
                                                                                16.0,
                                                                          ),
                                                                        ),
                                                                      ),
                                                              ),
                                                              SizedBox(
                                                                  width: 6.0),
                                                              Expanded(
                                                                child: Column(
                                                                  crossAxisAlignment:
                                                                      CrossAxisAlignment
                                                                          .start,
                                                                  children: [
                                                                    Text(
                                                                      recentPost
                                                                          .vendor_name
                                                                          .toString(),
                                                                      style: ClassifiedAppTheme.of(
                                                                              context)
                                                                          .bodyMedium
                                                                          .override(
                                                                            fontFamily:
                                                                                'Satoshi',
                                                                            fontSize:
                                                                                11.0,
                                                                            fontWeight:
                                                                                FontWeight.w600,
                                                                            useGoogleFonts:
                                                                                false,
                                                                          ),
                                                                      maxLines:
                                                                          1,
                                                                      overflow:
                                                                          TextOverflow
                                                                              .ellipsis,
                                                                    ),
                                                                    Row(
                                                                      mainAxisSize:
                                                                          MainAxisSize
                                                                              .max,
                                                                      children: [
                                                                        Icon(
                                                                          Icons
                                                                              .location_on,
                                                                          color:
                                                                              Color(0xFF9E9E9E),
                                                                          size:
                                                                              12.0,
                                                                        ),
                                                                        SizedBox(
                                                                            width:
                                                                                2.0),
                                                                        Expanded(
                                                                          child:
                                                                              Text(
                                                                            recentPost.city.toString(),
                                                                            style: ClassifiedAppTheme.of(context).bodyMedium.override(
                                                                                  fontFamily: 'Satoshi',
                                                                                  fontSize: 10.0,
                                                                                  fontWeight: FontWeight.w400,
                                                                                  color: Color(0xFF9E9E9E),
                                                                                  useGoogleFonts: false,
                                                                                ),
                                                                            maxLines:
                                                                                1,
                                                                            overflow:
                                                                                TextOverflow.ellipsis,
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ],
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        );
                                      },
                                      childCount: _displayedPosts.length,
                                    ),
                                  ),
                                ),
                                // Loading indicator
                                if (_isLoadingMore)
                                  SliverToBoxAdapter(
                                    child: Padding(
                                      padding: EdgeInsets.all(16.0),
                                      child: Center(
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2.0,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                            ClassifiedAppTheme.of(context)
                                                .primary,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                // Bottom padding
                                SliverToBoxAdapter(
                                  child: SizedBox(height: 80.0),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    )
                  : Column(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          'assets/images/myProductsEmpty.png',
                          width: 120.0,
                          height: 120.0,
                          fit: BoxFit.contain,
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 28.0, 0.0, 0.0),
                          child: Text(
                            FFLocalizations.of(context).getText(
                              'r8fti1o5' /* No products yet */,
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 24.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 16.0, 0.0, 0.0),
                          child: Text(
                            FFLocalizations.of(context).getText(
                              'tvaeeupe' /* Your products list is empty pl... */,
                            ),
                            textAlign: TextAlign.center,
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 17.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              54.0, 28.0, 54.0, 0.0),
                          child: wrapWithModel(
                            model: _model.appBarModel,
                            updateCallback: () => setState(() {}),
                            child: AppButtonWidget(
                              text: 'Go to home',
                              action: () async {},
                            ),
                          ),
                        ),
                      ],
                    )
            ],
          ),
        ),
      ),
    );
  }

  Widget deletePostWidget(int post_id) {
    return Container(
      width: 300.0,
      height: 200.0,
      decoration: BoxDecoration(
        color: ClassifiedAppTheme.of(context).secondaryBackground,
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Padding(
        padding: EdgeInsets.all(20.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: Colors.orange,
              size: 48.0,
            ),
            SizedBox(height: 16.0),
            Text(
              'Delete Post',
              style: ClassifiedAppTheme.of(context).headlineSmall.override(
                    fontFamily: 'Satoshi',
                    fontSize: 20.0,
                    fontWeight: FontWeight.bold,
                    useGoogleFonts: false,
                  ),
            ),
            SizedBox(height: 8.0),
            Text(
              'Are you sure you want to delete this post? This action cannot be undone.',
              textAlign: TextAlign.center,
              style: ClassifiedAppTheme.of(context).bodyMedium.override(
                    fontFamily: 'Satoshi',
                    fontSize: 14.0,
                    color: Color(0xFF6B7280),
                    useGoogleFonts: false,
                  ),
            ),
            SizedBox(height: 20.0),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: Color(0xFFE5E7EB)),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                    ),
                    child: Text(
                      'Cancel',
                      style: ClassifiedAppTheme.of(context).bodyMedium.override(
                            fontFamily: 'Satoshi',
                            fontSize: 14.0,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF6B7280),
                            useGoogleFonts: false,
                          ),
                    ),
                  ),
                ),
                SizedBox(width: 12.0),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      deletePost(post_id);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                    ),
                    child: Text(
                      'Delete',
                      style: ClassifiedAppTheme.of(context).bodyMedium.override(
                            fontFamily: 'Satoshi',
                            fontSize: 14.0,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                            useGoogleFonts: false,
                          ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
